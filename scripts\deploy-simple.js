const { ethers } = require("hardhat");

async function main() {
  console.log("🚀 开始部署简化版 Web3 合约...\n");

  const [deployer] = await ethers.getSigners();
  console.log("部署账户:", deployer.address);
  console.log("账户余额:", ethers.formatEther(await deployer.provider.getBalance(deployer.address)), "ETH\n");

  const deployedContracts = {};

  try {
    // 1. 部署 SimpleToken
    console.log("📄 部署 SimpleToken...");
    const SimpleToken = await ethers.getContractFactory("SimpleToken");
    const simpleToken = await SimpleToken.deploy(
      "Web3 Token",
      "W3T",
      ethers.parseEther("1000000"),
      deployer.address,
      { gasLimit: 2000000 }
    );
    await simpleToken.waitForDeployment();
    deployedContracts.SimpleToken = await simpleToken.getAddress();
    console.log("✅ SimpleToken 部署成功:", deployedContracts.SimpleToken);

    // 2. 部署 SimpleDAO
    console.log("\n🏛️ 部署 SimpleDAO...");
    const SimpleDAO = await ethers.getContractFactory("SimpleDAO");
    const simpleDAO = await SimpleDAO.deploy(
      deployedContracts.SimpleToken,
      deployer.address,
      { gasLimit: 3000000 }
    );
    await simpleDAO.waitForDeployment();
    deployedContracts.SimpleDAO = await simpleDAO.getAddress();
    console.log("✅ SimpleDAO 部署成功:", deployedContracts.SimpleDAO);

    console.log("\n🎉 部署完成！合约地址:");
    console.log("=====================================");
    Object.entries(deployedContracts).forEach(([name, address]) => {
      console.log(`${name.padEnd(20)}: ${address}`);
    });
    console.log("=====================================");

    return deployedContracts;

  } catch (error) {
    console.error("\n❌ 部署失败:", error.message);
    throw error;
  }
}

if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

module.exports = main;
