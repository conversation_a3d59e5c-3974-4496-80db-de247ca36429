/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/index";
exports.ids = ["pages/index"];
exports.modules = {

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./pages/_app.js\");\n/* harmony import */ var _pages_index_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages\\index.js */ \"./pages/index.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_index_js__WEBPACK_IMPORTED_MODULE_5__]);\n([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_index_js__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/index\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _pages_index_js__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./pages/_app.js":
/*!***********************!*\
  !*** ./pages/_app.js ***!
  \***********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App),\n/* harmony export */   useWeb3: () => (/* binding */ useWeb3)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _frontend_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../frontend/styles/globals.css */ \"./frontend/styles/globals.css\");\n/* harmony import */ var _frontend_styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_frontend_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ethers */ \"ethers\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([ethers__WEBPACK_IMPORTED_MODULE_3__]);\nethers__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// Web3 上下文\n\nconst Web3Context = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_2__.createContext)();\nconst useWeb3 = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(Web3Context);\n    if (!context) {\n        throw new Error(\"useWeb3 must be used within a Web3Provider\");\n    }\n    return context;\n};\nfunction Web3Provider({ children }) {\n    const [account, setAccount] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [provider, setProvider] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [signer, setSigner] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [chainId, setChainId] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [isConnecting, setIsConnecting] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // 连接钱包\n    const connectWallet = async ()=>{\n        if (typeof window.ethereum === \"undefined\") {\n            alert(\"请安装 MetaMask!\");\n            return;\n        }\n        setIsConnecting(true);\n        try {\n            // 请求账户访问\n            await window.ethereum.request({\n                method: \"eth_requestAccounts\"\n            });\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_3__.ethers.BrowserProvider(window.ethereum);\n            const signer = await provider.getSigner();\n            const address = await signer.getAddress();\n            const network = await provider.getNetwork();\n            setProvider(provider);\n            setSigner(signer);\n            setAccount(address);\n            setChainId(Number(network.chainId));\n            console.log(\"钱包连接成功:\", address);\n        } catch (error) {\n            console.error(\"连接钱包失败:\", error);\n            alert(\"连接钱包失败: \" + error.message);\n        } finally{\n            setIsConnecting(false);\n        }\n    };\n    // 断开钱包连接\n    const disconnectWallet = ()=>{\n        setAccount(null);\n        setProvider(null);\n        setSigner(null);\n        setChainId(null);\n    };\n    // 监听账户变化\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (typeof window.ethereum !== \"undefined\") {\n            window.ethereum.on(\"accountsChanged\", (accounts)=>{\n                if (accounts.length === 0) {\n                    disconnectWallet();\n                } else {\n                    connectWallet();\n                }\n            });\n            window.ethereum.on(\"chainChanged\", (chainId)=>{\n                setChainId(parseInt(chainId, 16));\n            });\n            // 检查是否已经连接\n            window.ethereum.request({\n                method: \"eth_accounts\"\n            }).then((accounts)=>{\n                if (accounts.length > 0) {\n                    connectWallet();\n                }\n            });\n        }\n        return ()=>{\n            if (typeof window.ethereum !== \"undefined\") {\n                window.ethereum.removeAllListeners(\"accountsChanged\");\n                window.ethereum.removeAllListeners(\"chainChanged\");\n            }\n        };\n    }, []);\n    const value = {\n        account,\n        provider,\n        signer,\n        chainId,\n        isConnecting,\n        connectWallet,\n        disconnectWallet,\n        isConnected: !!account\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Web3Context.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\_app.js\",\n        lineNumber: 108,\n        columnNumber: 5\n    }, this);\n}\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Web3Provider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...pageProps\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\_app.js\",\n                lineNumber: 118,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\_app.js\",\n            lineNumber: 117,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\_app.js\",\n        lineNumber: 116,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9fYXBwLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBd0M7QUFDSTtBQUNaO0FBRWhDLFdBQVc7QUFDdUM7QUFFbEQsTUFBTUssNEJBQWNGLG9EQUFhQTtBQUUxQixNQUFNRyxVQUFVO0lBQ3JCLE1BQU1DLFVBQVVILGlEQUFVQSxDQUFDQztJQUMzQixJQUFJLENBQUNFLFNBQVM7UUFDWixNQUFNLElBQUlDLE1BQU07SUFDbEI7SUFDQSxPQUFPRDtBQUNULEVBQUU7QUFFRixTQUFTRSxhQUFhLEVBQUVDLFFBQVEsRUFBRTtJQUNoQyxNQUFNLENBQUNDLFNBQVNDLFdBQVcsR0FBR1osK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDYSxVQUFVQyxZQUFZLEdBQUdkLCtDQUFRQSxDQUFDO0lBQ3pDLE1BQU0sQ0FBQ2UsUUFBUUMsVUFBVSxHQUFHaEIsK0NBQVFBLENBQUM7SUFDckMsTUFBTSxDQUFDaUIsU0FBU0MsV0FBVyxHQUFHbEIsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDbUIsY0FBY0MsZ0JBQWdCLEdBQUdwQiwrQ0FBUUEsQ0FBQztJQUVqRCxPQUFPO0lBQ1AsTUFBTXFCLGdCQUFnQjtRQUNwQixJQUFJLE9BQU9DLE9BQU9DLFFBQVEsS0FBSyxhQUFhO1lBQzFDQyxNQUFNO1lBQ047UUFDRjtRQUVBSixnQkFBZ0I7UUFDaEIsSUFBSTtZQUNGLFNBQVM7WUFDVCxNQUFNRSxPQUFPQyxRQUFRLENBQUNFLE9BQU8sQ0FBQztnQkFBRUMsUUFBUTtZQUFzQjtZQUU5RCxNQUFNYixXQUFXLElBQUlYLDBDQUFNQSxDQUFDeUIsZUFBZSxDQUFDTCxPQUFPQyxRQUFRO1lBQzNELE1BQU1SLFNBQVMsTUFBTUYsU0FBU2UsU0FBUztZQUN2QyxNQUFNQyxVQUFVLE1BQU1kLE9BQU9lLFVBQVU7WUFDdkMsTUFBTUMsVUFBVSxNQUFNbEIsU0FBU21CLFVBQVU7WUFFekNsQixZQUFZRDtZQUNaRyxVQUFVRDtZQUNWSCxXQUFXaUI7WUFDWFgsV0FBV2UsT0FBT0YsUUFBUWQsT0FBTztZQUVqQ2lCLFFBQVFDLEdBQUcsQ0FBQyxXQUFXTjtRQUN6QixFQUFFLE9BQU9PLE9BQU87WUFDZEYsUUFBUUUsS0FBSyxDQUFDLFdBQVdBO1lBQ3pCWixNQUFNLGFBQWFZLE1BQU1DLE9BQU87UUFDbEMsU0FBVTtZQUNSakIsZ0JBQWdCO1FBQ2xCO0lBQ0Y7SUFFQSxTQUFTO0lBQ1QsTUFBTWtCLG1CQUFtQjtRQUN2QjFCLFdBQVc7UUFDWEUsWUFBWTtRQUNaRSxVQUFVO1FBQ1ZFLFdBQVc7SUFDYjtJQUVBLFNBQVM7SUFDVGpCLGdEQUFTQSxDQUFDO1FBQ1IsSUFBSSxPQUFPcUIsT0FBT0MsUUFBUSxLQUFLLGFBQWE7WUFDMUNELE9BQU9DLFFBQVEsQ0FBQ2dCLEVBQUUsQ0FBQyxtQkFBbUIsQ0FBQ0M7Z0JBQ3JDLElBQUlBLFNBQVNDLE1BQU0sS0FBSyxHQUFHO29CQUN6Qkg7Z0JBQ0YsT0FBTztvQkFDTGpCO2dCQUNGO1lBQ0Y7WUFFQUMsT0FBT0MsUUFBUSxDQUFDZ0IsRUFBRSxDQUFDLGdCQUFnQixDQUFDdEI7Z0JBQ2xDQyxXQUFXd0IsU0FBU3pCLFNBQVM7WUFDL0I7WUFFQSxXQUFXO1lBQ1hLLE9BQU9DLFFBQVEsQ0FBQ0UsT0FBTyxDQUFDO2dCQUFFQyxRQUFRO1lBQWUsR0FDOUNpQixJQUFJLENBQUNILENBQUFBO2dCQUNKLElBQUlBLFNBQVNDLE1BQU0sR0FBRyxHQUFHO29CQUN2QnBCO2dCQUNGO1lBQ0Y7UUFDSjtRQUVBLE9BQU87WUFDTCxJQUFJLE9BQU9DLE9BQU9DLFFBQVEsS0FBSyxhQUFhO2dCQUMxQ0QsT0FBT0MsUUFBUSxDQUFDcUIsa0JBQWtCLENBQUM7Z0JBQ25DdEIsT0FBT0MsUUFBUSxDQUFDcUIsa0JBQWtCLENBQUM7WUFDckM7UUFDRjtJQUNGLEdBQUcsRUFBRTtJQUVMLE1BQU1DLFFBQVE7UUFDWmxDO1FBQ0FFO1FBQ0FFO1FBQ0FFO1FBQ0FFO1FBQ0FFO1FBQ0FpQjtRQUNBUSxhQUFhLENBQUMsQ0FBQ25DO0lBQ2pCO0lBRUEscUJBQ0UsOERBQUNOLFlBQVkwQyxRQUFRO1FBQUNGLE9BQU9BO2tCQUMxQm5DOzs7Ozs7QUFHUDtBQUVlLFNBQVNzQyxJQUFJLEVBQUVDLFNBQVMsRUFBRUMsU0FBUyxFQUFFO0lBQ2xELHFCQUNFLDhEQUFDekM7a0JBQ0MsNEVBQUMwQztZQUFJQyxXQUFVO3NCQUNiLDRFQUFDSDtnQkFBVyxHQUFHQyxTQUFTOzs7Ozs7Ozs7Ozs7Ozs7O0FBSWhDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd2ViMy1lY29zeXN0ZW0vLi9wYWdlcy9fYXBwLmpzP2UwYWQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICcuLi9mcm9udGVuZC9zdHlsZXMvZ2xvYmFscy5jc3MnO1xuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IGV0aGVycyB9IGZyb20gJ2V0aGVycyc7XG5cbi8vIFdlYjMg5LiK5LiL5paHXG5pbXBvcnQgeyBjcmVhdGVDb250ZXh0LCB1c2VDb250ZXh0IH0gZnJvbSAncmVhY3QnO1xuXG5jb25zdCBXZWIzQ29udGV4dCA9IGNyZWF0ZUNvbnRleHQoKTtcblxuZXhwb3J0IGNvbnN0IHVzZVdlYjMgPSAoKSA9PiB7XG4gIGNvbnN0IGNvbnRleHQgPSB1c2VDb250ZXh0KFdlYjNDb250ZXh0KTtcbiAgaWYgKCFjb250ZXh0KSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCd1c2VXZWIzIG11c3QgYmUgdXNlZCB3aXRoaW4gYSBXZWIzUHJvdmlkZXInKTtcbiAgfVxuICByZXR1cm4gY29udGV4dDtcbn07XG5cbmZ1bmN0aW9uIFdlYjNQcm92aWRlcih7IGNoaWxkcmVuIH0pIHtcbiAgY29uc3QgW2FjY291bnQsIHNldEFjY291bnRdID0gdXNlU3RhdGUobnVsbCk7XG4gIGNvbnN0IFtwcm92aWRlciwgc2V0UHJvdmlkZXJdID0gdXNlU3RhdGUobnVsbCk7XG4gIGNvbnN0IFtzaWduZXIsIHNldFNpZ25lcl0gPSB1c2VTdGF0ZShudWxsKTtcbiAgY29uc3QgW2NoYWluSWQsIHNldENoYWluSWRdID0gdXNlU3RhdGUobnVsbCk7XG4gIGNvbnN0IFtpc0Nvbm5lY3RpbmcsIHNldElzQ29ubmVjdGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG5cbiAgLy8g6L+e5o6l6ZKx5YyFXG4gIGNvbnN0IGNvbm5lY3RXYWxsZXQgPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cuZXRoZXJldW0gPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICBhbGVydCgn6K+35a6J6KOFIE1ldGFNYXNrIScpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIHNldElzQ29ubmVjdGluZyh0cnVlKTtcbiAgICB0cnkge1xuICAgICAgLy8g6K+35rGC6LSm5oi36K6/6ZeuXG4gICAgICBhd2FpdCB3aW5kb3cuZXRoZXJldW0ucmVxdWVzdCh7IG1ldGhvZDogJ2V0aF9yZXF1ZXN0QWNjb3VudHMnIH0pO1xuICAgICAgXG4gICAgICBjb25zdCBwcm92aWRlciA9IG5ldyBldGhlcnMuQnJvd3NlclByb3ZpZGVyKHdpbmRvdy5ldGhlcmV1bSk7XG4gICAgICBjb25zdCBzaWduZXIgPSBhd2FpdCBwcm92aWRlci5nZXRTaWduZXIoKTtcbiAgICAgIGNvbnN0IGFkZHJlc3MgPSBhd2FpdCBzaWduZXIuZ2V0QWRkcmVzcygpO1xuICAgICAgY29uc3QgbmV0d29yayA9IGF3YWl0IHByb3ZpZGVyLmdldE5ldHdvcmsoKTtcblxuICAgICAgc2V0UHJvdmlkZXIocHJvdmlkZXIpO1xuICAgICAgc2V0U2lnbmVyKHNpZ25lcik7XG4gICAgICBzZXRBY2NvdW50KGFkZHJlc3MpO1xuICAgICAgc2V0Q2hhaW5JZChOdW1iZXIobmV0d29yay5jaGFpbklkKSk7XG5cbiAgICAgIGNvbnNvbGUubG9nKCfpkrHljIXov57mjqXmiJDlip86JywgYWRkcmVzcyk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+i/nuaOpemSseWMheWksei0pTonLCBlcnJvcik7XG4gICAgICBhbGVydCgn6L+e5o6l6ZKx5YyF5aSx6LSlOiAnICsgZXJyb3IubWVzc2FnZSk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldElzQ29ubmVjdGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIC8vIOaWreW8gOmSseWMhei/nuaOpVxuICBjb25zdCBkaXNjb25uZWN0V2FsbGV0ID0gKCkgPT4ge1xuICAgIHNldEFjY291bnQobnVsbCk7XG4gICAgc2V0UHJvdmlkZXIobnVsbCk7XG4gICAgc2V0U2lnbmVyKG51bGwpO1xuICAgIHNldENoYWluSWQobnVsbCk7XG4gIH07XG5cbiAgLy8g55uR5ZCs6LSm5oi35Y+Y5YyWXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cuZXRoZXJldW0gIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICB3aW5kb3cuZXRoZXJldW0ub24oJ2FjY291bnRzQ2hhbmdlZCcsIChhY2NvdW50cykgPT4ge1xuICAgICAgICBpZiAoYWNjb3VudHMubGVuZ3RoID09PSAwKSB7XG4gICAgICAgICAgZGlzY29ubmVjdFdhbGxldCgpO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIGNvbm5lY3RXYWxsZXQoKTtcbiAgICAgICAgfVxuICAgICAgfSk7XG5cbiAgICAgIHdpbmRvdy5ldGhlcmV1bS5vbignY2hhaW5DaGFuZ2VkJywgKGNoYWluSWQpID0+IHtcbiAgICAgICAgc2V0Q2hhaW5JZChwYXJzZUludChjaGFpbklkLCAxNikpO1xuICAgICAgfSk7XG5cbiAgICAgIC8vIOajgOafpeaYr+WQpuW3sue7j+i/nuaOpVxuICAgICAgd2luZG93LmV0aGVyZXVtLnJlcXVlc3QoeyBtZXRob2Q6ICdldGhfYWNjb3VudHMnIH0pXG4gICAgICAgIC50aGVuKGFjY291bnRzID0+IHtcbiAgICAgICAgICBpZiAoYWNjb3VudHMubGVuZ3RoID4gMCkge1xuICAgICAgICAgICAgY29ubmVjdFdhbGxldCgpO1xuICAgICAgICAgIH1cbiAgICAgICAgfSk7XG4gICAgfVxuXG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIGlmICh0eXBlb2Ygd2luZG93LmV0aGVyZXVtICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICB3aW5kb3cuZXRoZXJldW0ucmVtb3ZlQWxsTGlzdGVuZXJzKCdhY2NvdW50c0NoYW5nZWQnKTtcbiAgICAgICAgd2luZG93LmV0aGVyZXVtLnJlbW92ZUFsbExpc3RlbmVycygnY2hhaW5DaGFuZ2VkJyk7XG4gICAgICB9XG4gICAgfTtcbiAgfSwgW10pO1xuXG4gIGNvbnN0IHZhbHVlID0ge1xuICAgIGFjY291bnQsXG4gICAgcHJvdmlkZXIsXG4gICAgc2lnbmVyLFxuICAgIGNoYWluSWQsXG4gICAgaXNDb25uZWN0aW5nLFxuICAgIGNvbm5lY3RXYWxsZXQsXG4gICAgZGlzY29ubmVjdFdhbGxldCxcbiAgICBpc0Nvbm5lY3RlZDogISFhY2NvdW50LFxuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPFdlYjNDb250ZXh0LlByb3ZpZGVyIHZhbHVlPXt2YWx1ZX0+XG4gICAgICB7Y2hpbGRyZW59XG4gICAgPC9XZWIzQ29udGV4dC5Qcm92aWRlcj5cbiAgKTtcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQXBwKHsgQ29tcG9uZW50LCBwYWdlUHJvcHMgfSkge1xuICByZXR1cm4gKFxuICAgIDxXZWIzUHJvdmlkZXI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1ncmFkaWVudC10by1iciBmcm9tLWJsdWUtNTAgdG8taW5kaWdvLTEwMFwiPlxuICAgICAgICA8Q29tcG9uZW50IHsuLi5wYWdlUHJvcHN9IC8+XG4gICAgICA8L2Rpdj5cbiAgICA8L1dlYjNQcm92aWRlcj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsImV0aGVycyIsImNyZWF0ZUNvbnRleHQiLCJ1c2VDb250ZXh0IiwiV2ViM0NvbnRleHQiLCJ1c2VXZWIzIiwiY29udGV4dCIsIkVycm9yIiwiV2ViM1Byb3ZpZGVyIiwiY2hpbGRyZW4iLCJhY2NvdW50Iiwic2V0QWNjb3VudCIsInByb3ZpZGVyIiwic2V0UHJvdmlkZXIiLCJzaWduZXIiLCJzZXRTaWduZXIiLCJjaGFpbklkIiwic2V0Q2hhaW5JZCIsImlzQ29ubmVjdGluZyIsInNldElzQ29ubmVjdGluZyIsImNvbm5lY3RXYWxsZXQiLCJ3aW5kb3ciLCJldGhlcmV1bSIsImFsZXJ0IiwicmVxdWVzdCIsIm1ldGhvZCIsIkJyb3dzZXJQcm92aWRlciIsImdldFNpZ25lciIsImFkZHJlc3MiLCJnZXRBZGRyZXNzIiwibmV0d29yayIsImdldE5ldHdvcmsiLCJOdW1iZXIiLCJjb25zb2xlIiwibG9nIiwiZXJyb3IiLCJtZXNzYWdlIiwiZGlzY29ubmVjdFdhbGxldCIsIm9uIiwiYWNjb3VudHMiLCJsZW5ndGgiLCJwYXJzZUludCIsInRoZW4iLCJyZW1vdmVBbGxMaXN0ZW5lcnMiLCJ2YWx1ZSIsImlzQ29ubmVjdGVkIiwiUHJvdmlkZXIiLCJBcHAiLCJDb21wb25lbnQiLCJwYWdlUHJvcHMiLCJkaXYiLCJjbGFzc05hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./pages/_app.js\n");

/***/ }),

/***/ "./pages/index.js":
/*!************************!*\
  !*** ./pages/index.js ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _app__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./_app */ \"./pages/_app.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ethers */ \"ethers\");\n/* harmony import */ var _frontend_config_contracts_json__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../frontend/config/contracts.json */ \"./frontend/config/contracts.json\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_app__WEBPACK_IMPORTED_MODULE_3__, ethers__WEBPACK_IMPORTED_MODULE_4__]);\n([_app__WEBPACK_IMPORTED_MODULE_3__, ethers__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n// 简化的 ABI\nconst ERC20_ABI = [\n    \"function name() view returns (string)\",\n    \"function symbol() view returns (string)\",\n    \"function totalSupply() view returns (uint256)\",\n    \"function balanceOf(address) view returns (uint256)\",\n    \"function transfer(address to, uint256 amount) returns (bool)\",\n    \"function mint(address to, uint256 amount)\",\n    \"function burn(uint256 amount)\"\n];\nconst DAO_ABI = [\n    \"function governanceToken() view returns (address)\",\n    \"function proposalCount() view returns (uint256)\",\n    \"function treasuryBalance() view returns (uint256)\",\n    \"function createProposal(string description, uint8 proposalType, uint256 requestedAmount, address beneficiary) returns (uint256)\",\n    \"function vote(uint256 proposalId, bool support)\",\n    \"function getProposal(uint256 proposalId) view returns (uint256, address, string, uint8, uint256, address, uint256, uint256, uint256, uint256, uint8, bool)\",\n    \"function depositFunds() payable\"\n];\nfunction Home() {\n    const { account, signer, isConnected, connectWallet, chainId } = (0,_app__WEBPACK_IMPORTED_MODULE_3__.useWeb3)();\n    const [tokenInfo, setTokenInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [daoInfo, setDAOInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [transferAmount, setTransferAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [transferTo, setTransferTo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 加载合约信息\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isConnected && signer) {\n            loadContractInfo();\n        }\n    }, [\n        isConnected,\n        signer\n    ]);\n    const loadContractInfo = async ()=>{\n        setLoading(true);\n        try {\n            // 加载代币信息\n            const tokenContract = new ethers__WEBPACK_IMPORTED_MODULE_4__.ethers.Contract(_frontend_config_contracts_json__WEBPACK_IMPORTED_MODULE_5__.contracts.SimpleToken, ERC20_ABI, signer);\n            const [name, symbol, totalSupply, balance] = await Promise.all([\n                tokenContract.name(),\n                tokenContract.symbol(),\n                tokenContract.totalSupply(),\n                tokenContract.balanceOf(account)\n            ]);\n            setTokenInfo({\n                name,\n                symbol,\n                totalSupply: ethers__WEBPACK_IMPORTED_MODULE_4__.ethers.formatEther(totalSupply),\n                balance: ethers__WEBPACK_IMPORTED_MODULE_4__.ethers.formatEther(balance),\n                contract: tokenContract\n            });\n            // 加载 DAO 信息\n            const daoContract = new ethers__WEBPACK_IMPORTED_MODULE_4__.ethers.Contract(_frontend_config_contracts_json__WEBPACK_IMPORTED_MODULE_5__.contracts.SimpleDAO, DAO_ABI, signer);\n            const [proposalCount, treasuryBalance] = await Promise.all([\n                daoContract.proposalCount(),\n                daoContract.treasuryBalance()\n            ]);\n            setDAOInfo({\n                proposalCount: proposalCount.toString(),\n                treasuryBalance: ethers__WEBPACK_IMPORTED_MODULE_4__.ethers.formatEther(treasuryBalance),\n                contract: daoContract\n            });\n        } catch (error) {\n            console.error(\"加载合约信息失败:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleTransfer = async ()=>{\n        if (!tokenInfo || !transferAmount || !transferTo) return;\n        try {\n            setLoading(true);\n            const tx = await tokenInfo.contract.transfer(transferTo, ethers__WEBPACK_IMPORTED_MODULE_4__.ethers.parseEther(transferAmount));\n            await tx.wait();\n            alert(\"转账成功!\");\n            loadContractInfo(); // 重新加载余额\n            setTransferAmount(\"\");\n            setTransferTo(\"\");\n        } catch (error) {\n            console.error(\"转账失败:\", error);\n            alert(\"转账失败: \" + error.message);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleDepositToDAO = async ()=>{\n        if (!daoInfo) return;\n        try {\n            setLoading(true);\n            const tx = await daoInfo.contract.depositFunds({\n                value: ethers__WEBPACK_IMPORTED_MODULE_4__.ethers.parseEther(\"0.1\") // 存入 0.1 ETH\n            });\n            await tx.wait();\n            alert(\"存款成功!\");\n            loadContractInfo();\n        } catch (error) {\n            console.error(\"存款失败:\", error);\n            alert(\"存款失败: \" + error.message);\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Web3 生态系统\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"完整的 Web3 生态系统演示\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                        lineNumber: 138,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                        lineNumber: 139,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"text-center mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-5xl font-bold gradient-text mb-4\",\n                                children: \"Web3 生态系统\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                lineNumber: 146,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-600 mb-8\",\n                                children: \"体验完整的去中心化应用功能\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                lineNumber: 149,\n                                columnNumber: 11\n                            }, this),\n                            !isConnected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: connectWallet,\n                                className: \"btn-primary text-lg px-8 py-3\",\n                                children: \"连接钱包\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                lineNumber: 154,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg p-4 inline-block shadow-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"已连接账户\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                        lineNumber: 162,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"font-mono text-sm\",\n                                        children: account\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                        lineNumber: 163,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500\",\n                                        children: [\n                                            \"链 ID: \",\n                                            chainId\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                        lineNumber: 164,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                lineNumber: 161,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                        lineNumber: 145,\n                        columnNumber: 9\n                    }, this),\n                    isConnected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid-responsive\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold mb-4 text-gray-800\",\n                                        children: \"\\uD83D\\uDCB0 代币信息\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                        lineNumber: 173,\n                                        columnNumber: 15\n                                    }, this),\n                                    loading && !tokenInfo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"loading-spinner\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 178,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2\",\n                                                children: \"加载中...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 179,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                        lineNumber: 177,\n                                        columnNumber: 17\n                                    }, this) : tokenInfo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600\",\n                                                        children: \"名称:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                        lineNumber: 184,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold\",\n                                                        children: tokenInfo.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 183,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600\",\n                                                        children: \"符号:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                        lineNumber: 188,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold\",\n                                                        children: tokenInfo.symbol\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                        lineNumber: 189,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 187,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600\",\n                                                        children: \"总供应量:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                        lineNumber: 192,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold\",\n                                                        children: parseFloat(tokenInfo.totalSupply).toLocaleString()\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                        lineNumber: 193,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 191,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600\",\n                                                        children: \"我的余额:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold text-primary-600\",\n                                                        children: [\n                                                            parseFloat(tokenInfo.balance).toFixed(4),\n                                                            \" \",\n                                                            tokenInfo.symbol\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                        lineNumber: 197,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 195,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                        lineNumber: 182,\n                                        columnNumber: 17\n                                    }, this) : null\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                lineNumber: 172,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold mb-4 text-gray-800\",\n                                        children: \"\\uD83D\\uDCE4 转账代币\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                        lineNumber: 207,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"接收地址\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                        lineNumber: 212,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: transferTo,\n                                                        onChange: (e)=>setTransferTo(e.target.value),\n                                                        placeholder: \"0x...\",\n                                                        className: \"input-field\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                        lineNumber: 215,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 211,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"转账数量\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                        lineNumber: 224,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        value: transferAmount,\n                                                        onChange: (e)=>setTransferAmount(e.target.value),\n                                                        placeholder: \"0.0\",\n                                                        className: \"input-field\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                        lineNumber: 227,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 223,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleTransfer,\n                                                disabled: loading || !transferAmount || !transferTo,\n                                                className: \"btn-primary w-full disabled:opacity-50\",\n                                                children: loading ? \"处理中...\" : \"发送转账\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 235,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                        lineNumber: 210,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                lineNumber: 206,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold mb-4 text-gray-800\",\n                                        children: \"\\uD83C\\uDFDB️ DAO 治理\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                        lineNumber: 247,\n                                        columnNumber: 15\n                                    }, this),\n                                    loading && !daoInfo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"loading-spinner\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 252,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2\",\n                                                children: \"加载中...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 253,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                        lineNumber: 251,\n                                        columnNumber: 17\n                                    }, this) : daoInfo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600\",\n                                                        children: \"提案数量:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                        lineNumber: 258,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold\",\n                                                        children: daoInfo.proposalCount\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                        lineNumber: 259,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 257,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600\",\n                                                        children: \"资金池:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold\",\n                                                        children: [\n                                                            parseFloat(daoInfo.treasuryBalance).toFixed(4),\n                                                            \" ETH\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 261,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleDepositToDAO,\n                                                disabled: loading,\n                                                className: \"btn-primary w-full\",\n                                                children: loading ? \"处理中...\" : \"向 DAO 存入 0.1 ETH\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 265,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                        lineNumber: 256,\n                                        columnNumber: 17\n                                    }, this) : null\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                lineNumber: 246,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                        lineNumber: 170,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"mt-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-bold text-center mb-8 text-gray-800\",\n                                children: \"\\uD83C\\uDF1F 生态系统功能\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                lineNumber: 280,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid-responsive\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-4xl mb-4\",\n                                                children: \"\\uD83E\\uDE99\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 285,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-bold mb-2\",\n                                                children: \"ERC-20 代币\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 286,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"完整的代币功能，包括转账、铸造、燃烧等操作\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 287,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                        lineNumber: 284,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-4xl mb-4\",\n                                                children: \"\\uD83C\\uDFA8\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 292,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-bold mb-2\",\n                                                children: \"NFT 市场\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 293,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"支持 NFT 铸造、交易、拍卖等完整市场功能\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 294,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                        lineNumber: 291,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-4xl mb-4\",\n                                                children: \"\\uD83C\\uDFDB️\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 299,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-bold mb-2\",\n                                                children: \"DAO 治理\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 300,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"去中心化自治组织，支持提案、投票、资金管理\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 301,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                        lineNumber: 298,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-4xl mb-4\",\n                                                children: \"\\uD83D\\uDCB1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 306,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-bold mb-2\",\n                                                children: \"DeFi 协议\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 307,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"自动做市商、流动性挖矿、借贷等金融功能\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 308,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                        lineNumber: 305,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-4xl mb-4\",\n                                                children: \"\\uD83C\\uDF09\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 313,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-bold mb-2\",\n                                                children: \"跨链桥接\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 314,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"支持多链资产转移，连接不同区块链网络\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 315,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                        lineNumber: 312,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-4xl mb-4\",\n                                                children: \"\\uD83D\\uDD10\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 320,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-bold mb-2\",\n                                                children: \"多签钱包\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 321,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"企业级安全方案，支持多重签名验证\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 322,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                        lineNumber: 319,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                lineNumber: 283,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                        lineNumber: 279,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                lineNumber: 143,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/index.js\n");

/***/ }),

/***/ "./frontend/styles/globals.css":
/*!*************************************!*\
  !*** ./frontend/styles/globals.css ***!
  \*************************************/
/***/ (() => {



/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "next/head":
/*!****************************!*\
  !*** external "next/head" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/head");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "ethers":
/*!*************************!*\
  !*** external "ethers" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = import("ethers");;

/***/ }),

/***/ "./frontend/config/contracts.json":
/*!****************************************!*\
  !*** ./frontend/config/contracts.json ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"contracts":{"SimpleToken":"******************************************","SimpleDAO":"******************************************"},"network":{"chainId":31337,"name":"localhost"}}');

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();