/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/index"],{

/***/ "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cxiaot%5CDesktop%5Ccs%5Cpages%5Cindex.js&page=%2F!":
/*!********************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cxiaot%5CDesktop%5Ccs%5Cpages%5Cindex.js&page=%2F! ***!
  \********************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/\",\n      function () {\n        return __webpack_require__(/*! ./pages/index.js */ \"./pages/index.js\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWNsaWVudC1wYWdlcy1sb2FkZXIuanM/YWJzb2x1dGVQYWdlUGF0aD1DJTNBJTVDVXNlcnMlNUN4aWFvdCU1Q0Rlc2t0b3AlNUNjcyU1Q3BhZ2VzJTVDaW5kZXguanMmcGFnZT0lMkYhIiwibWFwcGluZ3MiOiI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLG1CQUFPLENBQUMsMENBQWtCO0FBQ3pDO0FBQ0E7QUFDQSxPQUFPLElBQVU7QUFDakIsTUFBTSxVQUFVO0FBQ2hCO0FBQ0EsT0FBTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLz9jOWUxIl0sInNvdXJjZXNDb250ZW50IjpbIlxuICAgICh3aW5kb3cuX19ORVhUX1AgPSB3aW5kb3cuX19ORVhUX1AgfHwgW10pLnB1c2goW1xuICAgICAgXCIvXCIsXG4gICAgICBmdW5jdGlvbiAoKSB7XG4gICAgICAgIHJldHVybiByZXF1aXJlKFwiLi9wYWdlcy9pbmRleC5qc1wiKTtcbiAgICAgIH1cbiAgICBdKTtcbiAgICBpZihtb2R1bGUuaG90KSB7XG4gICAgICBtb2R1bGUuaG90LmRpc3Bvc2UoZnVuY3Rpb24gKCkge1xuICAgICAgICB3aW5kb3cuX19ORVhUX1AucHVzaChbXCIvXCJdKVxuICAgICAgfSk7XG4gICAgfVxuICAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cxiaot%5CDesktop%5Ccs%5Cpages%5Cindex.js&page=%2F!\n"));

/***/ }),

/***/ "./pages/index.js":
/*!************************!*\
  !*** ./pages/index.js ***!
  \************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Home; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _app__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./_app */ \"./pages/_app.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ethers */ \"./node_modules/ethers/lib.esm/index.js\");\n/* harmony import */ var _frontend_config_contracts_json__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../frontend/config/contracts.json */ \"./frontend/config/contracts.json\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n// 简化的 ABI\nconst ERC20_ABI = [\n    \"function name() view returns (string)\",\n    \"function symbol() view returns (string)\",\n    \"function totalSupply() view returns (uint256)\",\n    \"function balanceOf(address) view returns (uint256)\",\n    \"function transfer(address to, uint256 amount) returns (bool)\",\n    \"function mint(address to, uint256 amount)\",\n    \"function burn(uint256 amount)\"\n];\nconst DAO_ABI = [\n    \"function governanceToken() view returns (address)\",\n    \"function proposalCount() view returns (uint256)\",\n    \"function treasuryBalance() view returns (uint256)\",\n    \"function createProposal(string description, uint8 proposalType, uint256 requestedAmount, address beneficiary) returns (uint256)\",\n    \"function vote(uint256 proposalId, bool support)\",\n    \"function getProposal(uint256 proposalId) view returns (uint256, address, string, uint8, uint256, address, uint256, uint256, uint256, uint256, uint8, bool)\",\n    \"function depositFunds() payable\"\n];\nfunction Home() {\n    _s();\n    const { account, signer, isConnected, connectWallet, chainId } = (0,_app__WEBPACK_IMPORTED_MODULE_3__.useWeb3)();\n    const [tokenInfo, setTokenInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [daoInfo, setDAOInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [transferAmount, setTransferAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [transferTo, setTransferTo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 加载合约信息\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isConnected && signer) {\n            loadContractInfo();\n        }\n    }, [\n        isConnected,\n        signer\n    ]);\n    const loadContractInfo = async ()=>{\n        setLoading(true);\n        try {\n            // 加载代币信息\n            const tokenContract = new ethers__WEBPACK_IMPORTED_MODULE_5__.ethers.Contract(_frontend_config_contracts_json__WEBPACK_IMPORTED_MODULE_4__.contracts.SimpleToken, ERC20_ABI, signer);\n            const [name, symbol, totalSupply, balance] = await Promise.all([\n                tokenContract.name(),\n                tokenContract.symbol(),\n                tokenContract.totalSupply(),\n                tokenContract.balanceOf(account)\n            ]);\n            setTokenInfo({\n                name,\n                symbol,\n                totalSupply: ethers__WEBPACK_IMPORTED_MODULE_5__.ethers.formatEther(totalSupply),\n                balance: ethers__WEBPACK_IMPORTED_MODULE_5__.ethers.formatEther(balance),\n                contract: tokenContract\n            });\n            // 加载 DAO 信息\n            const daoContract = new ethers__WEBPACK_IMPORTED_MODULE_5__.ethers.Contract(_frontend_config_contracts_json__WEBPACK_IMPORTED_MODULE_4__.contracts.SimpleDAO, DAO_ABI, signer);\n            const [proposalCount, treasuryBalance] = await Promise.all([\n                daoContract.proposalCount(),\n                daoContract.treasuryBalance()\n            ]);\n            setDAOInfo({\n                proposalCount: proposalCount.toString(),\n                treasuryBalance: ethers__WEBPACK_IMPORTED_MODULE_5__.ethers.formatEther(treasuryBalance),\n                contract: daoContract\n            });\n        } catch (error) {\n            console.error(\"加载合约信息失败:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleTransfer = async ()=>{\n        if (!tokenInfo || !transferAmount || !transferTo) return;\n        try {\n            setLoading(true);\n            const tx = await tokenInfo.contract.transfer(transferTo, ethers__WEBPACK_IMPORTED_MODULE_5__.ethers.parseEther(transferAmount));\n            await tx.wait();\n            alert(\"转账成功!\");\n            loadContractInfo(); // 重新加载余额\n            setTransferAmount(\"\");\n            setTransferTo(\"\");\n        } catch (error) {\n            console.error(\"转账失败:\", error);\n            alert(\"转账失败: \" + error.message);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleDepositToDAO = async ()=>{\n        if (!daoInfo) return;\n        try {\n            setLoading(true);\n            const tx = await daoInfo.contract.depositFunds({\n                value: ethers__WEBPACK_IMPORTED_MODULE_5__.ethers.parseEther(\"0.1\") // 存入 0.1 ETH\n            });\n            await tx.wait();\n            alert(\"存款成功!\");\n            loadContractInfo();\n        } catch (error) {\n            console.error(\"存款失败:\", error);\n            alert(\"存款失败: \" + error.message);\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Web3 生态系统\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"完整的 Web3 生态系统演示\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                        lineNumber: 138,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                        lineNumber: 139,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"text-center mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-5xl font-bold gradient-text mb-4\",\n                                children: \"Web3 生态系统\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                lineNumber: 146,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-600 mb-8\",\n                                children: \"体验完整的去中心化应用功能\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                lineNumber: 149,\n                                columnNumber: 11\n                            }, this),\n                            !isConnected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: connectWallet,\n                                className: \"btn-primary text-lg px-8 py-3\",\n                                children: \"连接钱包\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                lineNumber: 154,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg p-4 inline-block shadow-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"已连接账户\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                        lineNumber: 162,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"font-mono text-sm\",\n                                        children: account\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                        lineNumber: 163,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500\",\n                                        children: [\n                                            \"链 ID: \",\n                                            chainId\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                        lineNumber: 164,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                lineNumber: 161,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                        lineNumber: 145,\n                        columnNumber: 9\n                    }, this),\n                    isConnected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid-responsive\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold mb-4 text-gray-800\",\n                                        children: \"\\uD83D\\uDCB0 代币信息\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                        lineNumber: 173,\n                                        columnNumber: 15\n                                    }, this),\n                                    loading && !tokenInfo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"loading-spinner\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 178,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2\",\n                                                children: \"加载中...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 179,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                        lineNumber: 177,\n                                        columnNumber: 17\n                                    }, this) : tokenInfo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600\",\n                                                        children: \"名称:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                        lineNumber: 184,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold\",\n                                                        children: tokenInfo.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 183,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600\",\n                                                        children: \"符号:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                        lineNumber: 188,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold\",\n                                                        children: tokenInfo.symbol\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                        lineNumber: 189,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 187,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600\",\n                                                        children: \"总供应量:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                        lineNumber: 192,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold\",\n                                                        children: parseFloat(tokenInfo.totalSupply).toLocaleString()\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                        lineNumber: 193,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 191,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600\",\n                                                        children: \"我的余额:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold text-primary-600\",\n                                                        children: [\n                                                            parseFloat(tokenInfo.balance).toFixed(4),\n                                                            \" \",\n                                                            tokenInfo.symbol\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                        lineNumber: 197,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 195,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                        lineNumber: 182,\n                                        columnNumber: 17\n                                    }, this) : null\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                lineNumber: 172,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold mb-4 text-gray-800\",\n                                        children: \"\\uD83D\\uDCE4 转账代币\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                        lineNumber: 207,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"接收地址\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                        lineNumber: 212,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: transferTo,\n                                                        onChange: (e)=>setTransferTo(e.target.value),\n                                                        placeholder: \"0x...\",\n                                                        className: \"input-field\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                        lineNumber: 215,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 211,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"转账数量\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                        lineNumber: 224,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        value: transferAmount,\n                                                        onChange: (e)=>setTransferAmount(e.target.value),\n                                                        placeholder: \"0.0\",\n                                                        className: \"input-field\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                        lineNumber: 227,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 223,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleTransfer,\n                                                disabled: loading || !transferAmount || !transferTo,\n                                                className: \"btn-primary w-full disabled:opacity-50\",\n                                                children: loading ? \"处理中...\" : \"发送转账\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 235,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                        lineNumber: 210,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                lineNumber: 206,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold mb-4 text-gray-800\",\n                                        children: \"\\uD83C\\uDFDB️ DAO 治理\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                        lineNumber: 247,\n                                        columnNumber: 15\n                                    }, this),\n                                    loading && !daoInfo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"loading-spinner\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 252,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2\",\n                                                children: \"加载中...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 253,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                        lineNumber: 251,\n                                        columnNumber: 17\n                                    }, this) : daoInfo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600\",\n                                                        children: \"提案数量:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                        lineNumber: 258,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold\",\n                                                        children: daoInfo.proposalCount\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                        lineNumber: 259,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 257,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600\",\n                                                        children: \"资金池:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold\",\n                                                        children: [\n                                                            parseFloat(daoInfo.treasuryBalance).toFixed(4),\n                                                            \" ETH\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 261,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleDepositToDAO,\n                                                disabled: loading,\n                                                className: \"btn-primary w-full\",\n                                                children: loading ? \"处理中...\" : \"向 DAO 存入 0.1 ETH\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 265,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                        lineNumber: 256,\n                                        columnNumber: 17\n                                    }, this) : null\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                lineNumber: 246,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                        lineNumber: 170,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"mt-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-bold text-center mb-8 text-gray-800\",\n                                children: \"\\uD83C\\uDF1F 生态系统功能\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                lineNumber: 280,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid-responsive\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-4xl mb-4\",\n                                                children: \"\\uD83E\\uDE99\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 285,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-bold mb-2\",\n                                                children: \"ERC-20 代币\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 286,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"完整的代币功能，包括转账、铸造、燃烧等操作\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 287,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                        lineNumber: 284,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-4xl mb-4\",\n                                                children: \"\\uD83C\\uDFA8\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 292,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-bold mb-2\",\n                                                children: \"NFT 市场\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 293,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"支持 NFT 铸造、交易、拍卖等完整市场功能\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 294,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                        lineNumber: 291,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-4xl mb-4\",\n                                                children: \"\\uD83C\\uDFDB️\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 299,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-bold mb-2\",\n                                                children: \"DAO 治理\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 300,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"去中心化自治组织，支持提案、投票、资金管理\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 301,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                        lineNumber: 298,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-4xl mb-4\",\n                                                children: \"\\uD83D\\uDCB1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 306,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-bold mb-2\",\n                                                children: \"DeFi 协议\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 307,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"自动做市商、流动性挖矿、借贷等金融功能\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 308,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                        lineNumber: 305,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-4xl mb-4\",\n                                                children: \"\\uD83C\\uDF09\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 313,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-bold mb-2\",\n                                                children: \"跨链桥接\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 314,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"支持多链资产转移，连接不同区块链网络\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 315,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                        lineNumber: 312,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-4xl mb-4\",\n                                                children: \"\\uD83D\\uDD10\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 320,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-bold mb-2\",\n                                                children: \"多签钱包\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 321,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"企业级安全方案，支持多重签名验证\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                                lineNumber: 322,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                        lineNumber: 319,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                                lineNumber: 283,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                        lineNumber: 279,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cs\\\\pages\\\\index.js\",\n                lineNumber: 143,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Home, \"DbUWPRya7PFG/shrnzcYQMf6ph0=\", false, function() {\n    return [\n        _app__WEBPACK_IMPORTED_MODULE_3__.useWeb3\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/index.js\n"));

/***/ }),

/***/ "./node_modules/next/head.js":
/*!***********************************!*\
  !*** ./node_modules/next/head.js ***!
  \***********************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("module.exports = __webpack_require__(/*! ./dist/shared/lib/head */ \"./node_modules/next/dist/shared/lib/head.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9oZWFkLmpzIiwibWFwcGluZ3MiOiJBQUFBLGlIQUFrRCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbmV4dC9oZWFkLmpzPzg4NDkiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Rpc3Qvc2hhcmVkL2xpYi9oZWFkJylcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/next/head.js\n"));

/***/ }),

/***/ "./frontend/config/contracts.json":
/*!****************************************!*\
  !*** ./frontend/config/contracts.json ***!
  \****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"contracts":{"SimpleToken":"0x5FbDB2315678afecb367f032d93F642f64180aa3","SimpleDAO":"0xe7f1725E7734CE288F8367e1Bb143E90bb3F0512"},"network":{"chainId":31337,"name":"localhost"}}');

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["pages/_app","main"], function() { return __webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cxiaot%5CDesktop%5Ccs%5Cpages%5Cindex.js&page=%2F!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);