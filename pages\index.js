import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useWeb3 } from './_app';
import { ethers } from 'ethers';
import contractsConfig from '../frontend/config/contracts.json';

// 简化的 ABI
const ERC20_ABI = [
  "function name() view returns (string)",
  "function symbol() view returns (string)",
  "function totalSupply() view returns (uint256)",
  "function balanceOf(address) view returns (uint256)",
  "function transfer(address to, uint256 amount) returns (bool)",
  "function mint(address to, uint256 amount)",
  "function burn(uint256 amount)"
];

const DAO_ABI = [
  "function governanceToken() view returns (address)",
  "function proposalCount() view returns (uint256)",
  "function treasuryBalance() view returns (uint256)",
  "function createProposal(string description, uint8 proposalType, uint256 requestedAmount, address beneficiary) returns (uint256)",
  "function vote(uint256 proposalId, bool support)",
  "function getProposal(uint256 proposalId) view returns (uint256, address, string, uint8, uint256, address, uint256, uint256, uint256, uint256, uint8, bool)",
  "function depositFunds() payable"
];

export default function Home() {
  const { account, signer, isConnected, connectWallet, chainId } = useWeb3();
  const [tokenInfo, setTokenInfo] = useState(null);
  const [daoInfo, setDAOInfo] = useState(null);
  const [loading, setLoading] = useState(false);
  const [transferAmount, setTransferAmount] = useState('');
  const [transferTo, setTransferTo] = useState('');

  // 加载合约信息
  useEffect(() => {
    if (isConnected && signer) {
      loadContractInfo();
    }
  }, [isConnected, signer]);

  const loadContractInfo = async () => {
    setLoading(true);
    try {
      // 加载代币信息
      const tokenContract = new ethers.Contract(
        contractsConfig.contracts.SimpleToken,
        ERC20_ABI,
        signer
      );

      const [name, symbol, totalSupply, balance] = await Promise.all([
        tokenContract.name(),
        tokenContract.symbol(),
        tokenContract.totalSupply(),
        tokenContract.balanceOf(account)
      ]);

      setTokenInfo({
        name,
        symbol,
        totalSupply: ethers.formatEther(totalSupply),
        balance: ethers.formatEther(balance),
        contract: tokenContract
      });

      // 加载 DAO 信息
      const daoContract = new ethers.Contract(
        contractsConfig.contracts.SimpleDAO,
        DAO_ABI,
        signer
      );

      const [proposalCount, treasuryBalance] = await Promise.all([
        daoContract.proposalCount(),
        daoContract.treasuryBalance()
      ]);

      setDAOInfo({
        proposalCount: proposalCount.toString(),
        treasuryBalance: ethers.formatEther(treasuryBalance),
        contract: daoContract
      });

    } catch (error) {
      console.error('加载合约信息失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleTransfer = async () => {
    if (!tokenInfo || !transferAmount || !transferTo) return;

    try {
      setLoading(true);
      const tx = await tokenInfo.contract.transfer(
        transferTo,
        ethers.parseEther(transferAmount)
      );
      await tx.wait();
      alert('转账成功!');
      loadContractInfo(); // 重新加载余额
      setTransferAmount('');
      setTransferTo('');
    } catch (error) {
      console.error('转账失败:', error);
      alert('转账失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleDepositToDAO = async () => {
    if (!daoInfo) return;

    try {
      setLoading(true);
      const tx = await daoInfo.contract.depositFunds({
        value: ethers.parseEther('0.1') // 存入 0.1 ETH
      });
      await tx.wait();
      alert('存款成功!');
      loadContractInfo();
    } catch (error) {
      console.error('存款失败:', error);
      alert('存款失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Head>
        <title>Web3 生态系统</title>
        <meta name="description" content="完整的 Web3 生态系统演示" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <main className="container mx-auto px-4 py-8">
        {/* 头部 */}
        <header className="text-center mb-12">
          <h1 className="text-5xl font-bold gradient-text mb-4">
            Web3 生态系统
          </h1>
          <p className="text-xl text-gray-600 mb-8">
            体验完整的去中心化应用功能
          </p>
          
          {!isConnected ? (
            <button
              onClick={connectWallet}
              className="btn-primary text-lg px-8 py-3"
            >
              连接钱包
            </button>
          ) : (
            <div className="bg-white rounded-lg p-4 inline-block shadow-lg">
              <p className="text-sm text-gray-600">已连接账户</p>
              <p className="font-mono text-sm">{account}</p>
              <p className="text-xs text-gray-500">链 ID: {chainId}</p>
            </div>
          )}
        </header>

        {isConnected && (
          <div className="grid-responsive">
            {/* 代币信息卡片 */}
            <div className="card">
              <h2 className="text-2xl font-bold mb-4 text-gray-800">
                💰 代币信息
              </h2>
              {loading && !tokenInfo ? (
                <div className="flex items-center justify-center py-8">
                  <div className="loading-spinner"></div>
                  <span className="ml-2">加载中...</span>
                </div>
              ) : tokenInfo ? (
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">名称:</span>
                    <span className="font-semibold">{tokenInfo.name}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">符号:</span>
                    <span className="font-semibold">{tokenInfo.symbol}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">总供应量:</span>
                    <span className="font-semibold">{parseFloat(tokenInfo.totalSupply).toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">我的余额:</span>
                    <span className="font-semibold text-primary-600">
                      {parseFloat(tokenInfo.balance).toFixed(4)} {tokenInfo.symbol}
                    </span>
                  </div>
                </div>
              ) : null}
            </div>

            {/* 转账功能 */}
            <div className="card">
              <h2 className="text-2xl font-bold mb-4 text-gray-800">
                📤 转账代币
              </h2>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    接收地址
                  </label>
                  <input
                    type="text"
                    value={transferTo}
                    onChange={(e) => setTransferTo(e.target.value)}
                    placeholder="0x..."
                    className="input-field"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    转账数量
                  </label>
                  <input
                    type="number"
                    value={transferAmount}
                    onChange={(e) => setTransferAmount(e.target.value)}
                    placeholder="0.0"
                    className="input-field"
                  />
                </div>
                <button
                  onClick={handleTransfer}
                  disabled={loading || !transferAmount || !transferTo}
                  className="btn-primary w-full disabled:opacity-50"
                >
                  {loading ? '处理中...' : '发送转账'}
                </button>
              </div>
            </div>

            {/* DAO 信息 */}
            <div className="card">
              <h2 className="text-2xl font-bold mb-4 text-gray-800">
                🏛️ DAO 治理
              </h2>
              {loading && !daoInfo ? (
                <div className="flex items-center justify-center py-8">
                  <div className="loading-spinner"></div>
                  <span className="ml-2">加载中...</span>
                </div>
              ) : daoInfo ? (
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-gray-600">提案数量:</span>
                    <span className="font-semibold">{daoInfo.proposalCount}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">资金池:</span>
                    <span className="font-semibold">{parseFloat(daoInfo.treasuryBalance).toFixed(4)} ETH</span>
                  </div>
                  <button
                    onClick={handleDepositToDAO}
                    disabled={loading}
                    className="btn-primary w-full"
                  >
                    {loading ? '处理中...' : '向 DAO 存入 0.1 ETH'}
                  </button>
                </div>
              ) : null}
            </div>
          </div>
        )}

        {/* 功能介绍 */}
        <section className="mt-16">
          <h2 className="text-3xl font-bold text-center mb-8 text-gray-800">
            🌟 生态系统功能
          </h2>
          <div className="grid-responsive">
            <div className="card text-center">
              <div className="text-4xl mb-4">🪙</div>
              <h3 className="text-xl font-bold mb-2">ERC-20 代币</h3>
              <p className="text-gray-600">
                完整的代币功能，包括转账、铸造、燃烧等操作
              </p>
            </div>
            <div className="card text-center">
              <div className="text-4xl mb-4">🎨</div>
              <h3 className="text-xl font-bold mb-2">NFT 市场</h3>
              <p className="text-gray-600">
                支持 NFT 铸造、交易、拍卖等完整市场功能
              </p>
            </div>
            <div className="card text-center">
              <div className="text-4xl mb-4">🏛️</div>
              <h3 className="text-xl font-bold mb-2">DAO 治理</h3>
              <p className="text-gray-600">
                去中心化自治组织，支持提案、投票、资金管理
              </p>
            </div>
            <div className="card text-center">
              <div className="text-4xl mb-4">💱</div>
              <h3 className="text-xl font-bold mb-2">DeFi 协议</h3>
              <p className="text-gray-600">
                自动做市商、流动性挖矿、借贷等金融功能
              </p>
            </div>
            <div className="card text-center">
              <div className="text-4xl mb-4">🌉</div>
              <h3 className="text-xl font-bold mb-2">跨链桥接</h3>
              <p className="text-gray-600">
                支持多链资产转移，连接不同区块链网络
              </p>
            </div>
            <div className="card text-center">
              <div className="text-4xl mb-4">🔐</div>
              <h3 className="text-xl font-bold mb-2">多签钱包</h3>
              <p className="text-gray-600">
                企业级安全方案，支持多重签名验证
              </p>
            </div>
          </div>
        </section>
      </main>
    </>
  );
}
