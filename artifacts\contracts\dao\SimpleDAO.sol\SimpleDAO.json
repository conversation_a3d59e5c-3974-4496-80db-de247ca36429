{"_format": "hh-sol-artifact-1", "contractName": "SimpleDAO", "sourceName": "contracts/dao/SimpleDAO.sol", "abi": [{"inputs": [{"internalType": "address", "name": "_governanceToken", "type": "address"}, {"internalType": "address", "name": "_owner", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "depositor", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "FundsDeposited", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "recipient", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "FundsWithdrawn", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "proposalId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "proposer", "type": "address"}, {"indexed": false, "internalType": "string", "name": "description", "type": "string"}, {"indexed": false, "internalType": "enum SimpleDAO.ProposalType", "name": "proposalType", "type": "uint8"}], "name": "ProposalCreated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "proposalId", "type": "uint256"}], "name": "ProposalExecuted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "proposalId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "voter", "type": "address"}, {"indexed": false, "internalType": "bool", "name": "support", "type": "bool"}, {"indexed": false, "internalType": "uint256", "name": "weight", "type": "uint256"}], "name": "VoteCast", "type": "event"}, {"inputs": [{"internalType": "string", "name": "description", "type": "string"}, {"internalType": "enum SimpleDAO.ProposalType", "name": "proposalType", "type": "uint8"}, {"internalType": "uint256", "name": "requestedAmount", "type": "uint256"}, {"internalType": "address", "name": "beneficiary", "type": "address"}], "name": "createProposal", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "depositFunds", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "proposalId", "type": "uint256"}], "name": "executeProposal", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "proposalId", "type": "uint256"}], "name": "getProposal", "outputs": [{"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "address", "name": "proposer", "type": "address"}, {"internalType": "string", "name": "description", "type": "string"}, {"internalType": "enum SimpleDAO.ProposalType", "name": "proposalType", "type": "uint8"}, {"internalType": "uint256", "name": "requestedAmount", "type": "uint256"}, {"internalType": "address", "name": "beneficiary", "type": "address"}, {"internalType": "uint256", "name": "forVotes", "type": "uint256"}, {"internalType": "uint256", "name": "againstVotes", "type": "uint256"}, {"internalType": "uint256", "name": "startTime", "type": "uint256"}, {"internalType": "uint256", "name": "endTime", "type": "uint256"}, {"internalType": "enum SimpleDAO.ProposalState", "name": "state", "type": "uint8"}, {"internalType": "bool", "name": "executed", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "proposalId", "type": "uint256"}, {"internalType": "address", "name": "user", "type": "address"}], "name": "getUserVote", "outputs": [{"internalType": "bool", "name": "voted", "type": "bool"}, {"internalType": "bool", "name": "choice", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "governanceToken", "outputs": [{"internalType": "contract IERC20", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "address", "name": "", "type": "address"}], "name": "hasVoted", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "memberContributions", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "proposalCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "proposalThreshold", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "proposals", "outputs": [{"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "address", "name": "proposer", "type": "address"}, {"internalType": "string", "name": "description", "type": "string"}, {"internalType": "enum SimpleDAO.ProposalType", "name": "proposalType", "type": "uint8"}, {"internalType": "uint256", "name": "requestedAmount", "type": "uint256"}, {"internalType": "address", "name": "beneficiary", "type": "address"}, {"internalType": "uint256", "name": "forVotes", "type": "uint256"}, {"internalType": "uint256", "name": "againstVotes", "type": "uint256"}, {"internalType": "uint256", "name": "startTime", "type": "uint256"}, {"internalType": "uint256", "name": "endTime", "type": "uint256"}, {"internalType": "enum SimpleDAO.ProposalState", "name": "state", "type": "uint8"}, {"internalType": "bool", "name": "executed", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "quorumPercentage", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "newThreshold", "type": "uint256"}], "name": "setProposalThreshold", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "newPercentage", "type": "uint256"}], "name": "setQuorumPercentage", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "newPeriod", "type": "uint256"}], "name": "setVotingPeriod", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "treasuryBalance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "proposalId", "type": "uint256"}], "name": "updateProposalState", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "proposalId", "type": "uint256"}, {"internalType": "bool", "name": "support", "type": "bool"}], "name": "vote", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "address", "name": "", "type": "address"}], "name": "voteChoice", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "votingPeriod", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"stateMutability": "payable", "type": "receive"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}